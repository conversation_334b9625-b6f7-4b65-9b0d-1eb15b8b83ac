import React from 'react';
import {
  Box,
  Typography,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  Chip,
  Checkbox,
  Button,
  Tooltip
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  HourglassEmpty as HourglassEmptyIcon
} from '@mui/icons-material';

const GroupsTable = ({
  groups,
  handleGroupSelect,
  handleMovesOnChange,
  handleNationalChange,
  handleCitationChange,
  handleCommendedChange,
  showMovesOn = false,
  showFinalAwards = false,
  showActionButton = true,
  limit = null,
  isCitationCategory = false
}) => {
  const displayGroups = limit ? groups.slice(0, limit) : groups;

  return (
    <TableContainer component={Paper} sx={{ mb: 4, boxShadow: 2 }}>
      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: 'rgba(59, 140, 110, 0.1)' }}>
            <TableCell sx={{ fontWeight: 'bold' }}>Group</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Score</TableCell>
            {showMovesOn && (
              <TableCell sx={{ fontWeight: 'bold' }}>Moves On</TableCell>
            )}
            {showFinalAwards && (
              <>
                <TableCell sx={{ fontWeight: 'bold' }}>{isCitationCategory ? 'Citation' : 'National'}</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Commended</TableCell>
              </>
            )}
            {showActionButton && (
              <TableCell sx={{ fontWeight: 'bold' }}>Action</TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {displayGroups.map((group) => (
            <TableRow
              key={group._id}
              sx={{
                '&:hover': {
                  backgroundColor: 'rgba(59, 140, 110, 0.05)',
                  cursor: 'pointer'
                }
              }}
              onClick={showActionButton ? undefined : () => handleGroupSelect(group)}
            >
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    Group: {group.maskedName}
                  </Typography>
                  {showMovesOn && group.isTopScore && (
                    <Tooltip title="This group is in the top 50% based on score">
                      <Chip
                        size="small"
                        label="Top 50%"
                        sx={{
                          ml: 1,
                          backgroundColor: '#3f51b5',
                          color: 'white',
                          height: '20px',
                          fontSize: '0.7rem'
                        }}
                      />
                    </Tooltip>
                  )}
                </Box>
              </TableCell>
              <TableCell>
                {group.hasScore ? (
                  <Chip
                    icon={<CheckCircleIcon sx={{ color: 'white' }} />}
                    label="Scored"
                    size="small"
                    sx={{
                      backgroundColor: '#3b8c6e',
                      color: 'white',
                      '& .MuiChip-icon': { color: 'white' }
                    }}
                  />
                ) : (
                  <Chip
                    icon={<HourglassEmptyIcon sx={{ color: 'white' }} />}
                    label="Not Scored"
                    size="small"
                    sx={{
                      backgroundColor: '#ff9800',
                      color: 'white',
                      '& .MuiChip-icon': { color: 'white' }
                    }}
                  />
                )}
              </TableCell>
              <TableCell>
                {group.score !== null ? (
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {group.score}
                  </Typography>
                ) : (
                  <Typography variant="body2" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                    No score
                  </Typography>
                )}
              </TableCell>

              {/* Moves On Checkbox - Only show if requested */}
              {showMovesOn && (
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Checkbox
                      checked={Boolean(group.movesOn)}
                      onChange={(e) => {
                        e.stopPropagation(); // Prevent row click
                        handleMovesOnChange(group, e.target.checked);
                      }}
                      disabled={!group.hasScore}
                      sx={{
                        color: '#3b8c6e',
                        '&.Mui-checked': {
                          color: '#3b8c6e',
                        },
                      }}
                      onClick={(e) => e.stopPropagation()} // Prevent row click
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        ml: 1,
                        color: group.hasScore ? 'text.primary' : 'text.disabled',
                        fontStyle: !group.hasScore ? 'italic' : 'normal'
                      }}
                    >
                      {group.movesOn ? 'Selected' : 'Not Selected'}
                    </Typography>
                  </Box>
                </TableCell>
              )}

              {/* National and Commended Checkboxes - Only show for Phase 2 judges */}
              {showFinalAwards && (
                <>
                  {/* National/Citation Checkbox */}
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Checkbox
                        checked={isCitationCategory ? Boolean(group.isCitation) : Boolean(group.isNational)}
                        onChange={(e) => {
                          e.stopPropagation(); // Prevent row click
                          if (isCitationCategory) {
                            handleCitationChange(group, e.target.checked);
                          } else {
                            handleNationalChange(group, e.target.checked);
                          }
                        }}
                        disabled={!group.hasScore}
                        sx={{
                          color: '#d4af37',
                          '&.Mui-checked': {
                            color: '#d4af37',
                          },
                        }}
                        onClick={(e) => e.stopPropagation()} // Prevent row click
                      />
                      <Typography
                        variant="body2"
                        sx={{
                          ml: 1,
                          color: group.hasScore ? 'text.primary' : 'text.disabled',
                          fontStyle: !group.hasScore ? 'italic' : 'normal'
                        }}
                      >
                        {(isCitationCategory ? group.isCitation : group.isNational)
                          ? (isCitationCategory ? 'Citation' : 'National')
                          : 'Not Selected'}
                      </Typography>
                    </Box>
                  </TableCell>

                  {/* Commended Checkbox */}
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Checkbox
                        checked={Boolean(group.isCommended)}
                        onChange={(e) => {
                          e.stopPropagation(); // Prevent row click
                          handleCommendedChange(group, e.target.checked);
                        }}
                        disabled={!group.hasScore}
                        sx={{
                          color: '#c0392b',
                          '&.Mui-checked': {
                            color: '#c0392b',
                          },
                        }}
                        onClick={(e) => e.stopPropagation()} // Prevent row click
                      />
                      <Typography
                        variant="body2"
                        sx={{
                          ml: 1,
                          color: group.hasScore ? 'text.primary' : 'text.disabled',
                          fontStyle: !group.hasScore ? 'italic' : 'normal'
                        }}
                      >
                        {group.isCommended ? 'Commended' : 'Not Selected'}
                      </Typography>
                    </Box>
                  </TableCell>
                </>
              )}

              {/* Action Button - Only show if requested */}
              {showActionButton && (
                <TableCell>
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent row click
                      handleGroupSelect(group);
                    }}
                    sx={{
                      backgroundColor: '#3b8c6e',
                      '&:hover': {
                        backgroundColor: '#2a6b4f',
                      }
                    }}
                  >
                    {group.hasScore ? 'Edit Score' : 'Score Now'}
                  </Button>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default GroupsTable;
